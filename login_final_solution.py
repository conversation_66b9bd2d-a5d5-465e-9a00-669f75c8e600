import requests
import hashlib
import time
import base64
import json
from urllib.parse import quote, urljoin
import os

def build_token(username, password, client_id, redirect_uri, access_code, timestamp):
    """根据真实浏览器请求构造token - 双重URL编码 + Base64编码"""
    token_data = {
        "name": username,
        "pwd": password,
        "redirect_uri": redirect_uri,
        "client_id": client_id,
        "access_code": access_code,
        "timestamp": timestamp
    }
    json_str = json.dumps(token_data, separators=(',', ':'))
    # 先URL编码JSON字符串
    url_encoded = quote(json_str)
    # 再Base64编码
    base64_encoded = base64.b64encode(url_encoded.encode('utf-8')).decode('utf-8')
    # 最后再次URL编码
    return quote(base64_encoded)

def get_captcha_key_and_image(session, base_url):
    """获取验证码key和图片"""
    # 1. 获取验证码key
    key_url = urljoin(base_url, "/Authorize/GetImgVerifyCodeKey")
    headers = {
        'Accept': '*/*',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Referer': f'{base_url}/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3'
    }
    
    try:
        response = session.get(key_url, headers=headers)
        response.raise_for_status()
        img_code_key = response.json()
        print(f"[+] 成功获取验证码Key: {img_code_key}")
    except Exception as e:
        print(f"[-] 获取验证码Key失败: {e}")
        return None, None
    
    # 2. 下载验证码图片
    img_url = urljoin(base_url, f"/Authorize/GetImgVerifyCode?key={img_code_key}")
    try:
        img_response = session.get(img_url, headers=headers)
        img_response.raise_for_status()
        
        # 保存验证码图片
        captcha_filename = f'captcha_{int(time.time())}.jpg'
        with open(captcha_filename, 'wb') as f:
            f.write(img_response.content)
        print(f"[+] 验证码图片已保存到: {captcha_filename}")
        
        return img_code_key, captcha_filename
    except Exception as e:
        print(f"[-] 下载验证码图片失败: {e}")
        return img_code_key, None

def perform_complete_login(username, password, img_code, img_code_key):
    """执行完整的登录流程 - 基于真实浏览器请求"""
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    
    # 1. 首先访问登录页面建立会话
    login_page_url = f"{base_url}/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3"
    
    page_headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Upgrade-Insecure-Requests': '1',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        page_response = session.get(login_page_url, headers=page_headers)
        page_response.raise_for_status()
        print(f"[+] 成功访问登录页面")
    except Exception as e:
        print(f"[-] 访问登录页面失败: {e}")
        return False, None
    
    # 2. 检查登录验证码要求
    client_id = "81c4e66b04066aa8da079f3e5d2bf1f3"
    verify_url = f"{base_url}/Authorize/GetLoginVerifCode?userAccount={username}&clientId={client_id}"
    
    verify_headers = {
        'Accept': '*/*',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Referer': login_page_url,
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        verify_response = session.get(verify_url, headers=verify_headers)
        verify_response.raise_for_status()
        verify_result = verify_response.json()
        print(f"[+] 验证码检查: {verify_result}")
    except Exception as e:
        print(f"[-] 验证码检查失败: {e}")
    
    # 3. 获取用户手机号信息
    mobile_url = f"{base_url}/Authorize/GetUserMobile?account={username}"
    try:
        mobile_response = session.get(mobile_url, headers=verify_headers)
        mobile_response.raise_for_status()
        mobile_result = mobile_response.json()
        print(f"[+] 用户信息: {mobile_result}")
    except Exception as e:
        print(f"[-] 获取用户信息失败: {e}")
    
    # 4. 构造登录参数
    redirect_uri = "https://apollo.siyscrm.com/account/login/loginsuccess"
    
    # 生成access_code (MD5哈希的大写形式)
    access_code = hashlib.md5(password.encode('utf-8')).hexdigest().upper()
    timestamp = int(time.time() * 1000)
    
    # 构建token
    token = build_token(username, password, client_id, redirect_uri, access_code, timestamp)
    
    # 5. 发送登录请求
    login_api_url = urljoin(base_url, "/Authorize/postV2")
    login_headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Referer': login_page_url,
        'token': token,
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    # 根据真实请求的格式构造payload
    login_payload = {
        'name': '',  # 在真实请求中这些字段是空的，因为信息在token中
        'pwd': '',
        'redirect_uri': '',
        'client_id': '',
        'img_verify_code': img_code,
        'img_verify_code_key': img_code_key,
        'is_mobile_verify': False
    }
    
    try:
        print(f"[+] 发送登录请求...")
        print(f"[+] Token: {token[:50]}...")
        print(f"[+] 验证码: {img_code}")
        
        response = session.post(login_api_url, headers=login_headers, json=login_payload)
        response.raise_for_status()
        
        # 检查响应内容类型
        content_type = response.headers.get('content-type', '')
        if 'application/json' in content_type:
            result = response.json()
            print(f"[+] 登录响应: {result}")
            
            if result.get("Status") == 0:
                print("[+] 登录成功!")
                return True, result
            else:
                print(f"[-] 登录失败: {result.get('Message', '未知错误')}")
                return False, result
        else:
            # 可能是重定向或其他响应
            print(f"[+] 响应状态码: {response.status_code}")
            print(f"[+] 响应头: {dict(response.headers)}")
            print(f"[+] 响应内容: {response.text[:500]}")
            
            if response.status_code == 200:
                print("[+] 登录可能成功 (非JSON响应)")
                return True, response.text
            else:
                return False, response.text
            
    except requests.exceptions.RequestException as e:
        print(f"[-] 登录请求异常: {e}")
        return False, None
    except json.JSONDecodeError:
        print(f"[-] 解析登录响应失败: {response.text}")
        return False, None

def main():
    """主函数"""
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    
    # 用户凭据
    username = "13995933053"
    password = "aabb6688"
    
    print("[+] 开始完整登录流程...")
    
    # 获取验证码
    img_code_key, captcha_filename = get_captcha_key_and_image(session, base_url)
    
    if not img_code_key or not captcha_filename:
        print("[-] 无法获取验证码，退出")
        return
    
    # 手动输入验证码
    print(f"[+] 请查看验证码图片: {captcha_filename}")
    img_code = input("请输入验证码: ").strip()
    
    if not img_code:
        print("[-] 验证码不能为空")
        return
    
    # 执行登录
    success, result = perform_complete_login(username, password, img_code, img_code_key)
    
    if success:
        print("[+] 登录流程完成!")
    else:
        print("[-] 登录失败")
    
    # 清理验证码文件
    try:
        if os.path.exists(captcha_filename):
            os.remove(captcha_filename)
            print(f"[+] 已清理验证码文件: {captcha_filename}")
    except:
        pass

if __name__ == "__main__":
    main()
