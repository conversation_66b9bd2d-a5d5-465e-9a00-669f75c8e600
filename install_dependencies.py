"""
安装Selenium自动化登录所需的依赖
"""

import subprocess
import sys
import os
import requests
import zipfile
import platform
from pathlib import Path

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def check_chrome_installed():
    """检查Chrome浏览器是否已安装"""
    try:
        if platform.system() == "Windows":
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]
            for path in chrome_paths:
                if os.path.exists(path):
                    print("✅ Chrome浏览器已安装")
                    return True
        else:
            # Linux/Mac
            result = subprocess.run(["which", "google-chrome"], capture_output=True)
            if result.returncode == 0:
                print("✅ Chrome浏览器已安装")
                return True
        
        print("❌ 未找到Chrome浏览器")
        return False
    except:
        print("❌ 检查Chrome浏览器时出错")
        return False

def download_chromedriver():
    """下载ChromeDriver"""
    try:
        print("🔄 正在下载ChromeDriver...")
        
        # 获取Chrome版本（简化版本，实际可能需要更复杂的版本检测）
        chrome_version = "133.0.6778.85"  # 当前稳定版本
        
        system = platform.system().lower()
        if system == "windows":
            driver_url = f"https://storage.googleapis.com/chrome-for-testing-public/{chrome_version}/win64/chromedriver-win64.zip"
            driver_name = "chromedriver.exe"
        elif system == "darwin":  # macOS
            driver_url = f"https://storage.googleapis.com/chrome-for-testing-public/{chrome_version}/mac-x64/chromedriver-mac-x64.zip"
            driver_name = "chromedriver"
        else:  # Linux
            driver_url = f"https://storage.googleapis.com/chrome-for-testing-public/{chrome_version}/linux64/chromedriver-linux64.zip"
            driver_name = "chromedriver"
        
        # 下载ChromeDriver
        response = requests.get(driver_url)
        if response.status_code == 200:
            zip_path = "chromedriver.zip"
            with open(zip_path, "wb") as f:
                f.write(response.content)
            
            # 解压
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(".")
            
            # 移动到当前目录
            extracted_folders = [f for f in os.listdir(".") if f.startswith("chromedriver-")]
            if extracted_folders:
                extracted_folder = extracted_folders[0]
                old_path = os.path.join(extracted_folder, driver_name)
                if os.path.exists(old_path):
                    os.rename(old_path, driver_name)
                    # 清理
                    import shutil
                    shutil.rmtree(extracted_folder)
            
            os.remove(zip_path)
            
            # 设置执行权限（Linux/Mac）
            if system != "windows":
                os.chmod(driver_name, 0o755)
            
            print("✅ ChromeDriver下载成功")
            return True
        else:
            print("❌ ChromeDriver下载失败")
            return False
            
    except Exception as e:
        print(f"❌ 下载ChromeDriver时出错: {e}")
        return False

def main():
    """主安装函数"""
    print("=" * 60)
    print("🔧 安装Selenium自动化登录依赖")
    print("=" * 60)
    
    # 1. 安装Python包
    print("\n📦 安装Python依赖包...")
    packages = [
        "selenium",
        "requests", 
        "Pillow",
        "webdriver-manager"  # 自动管理ChromeDriver
    ]
    
    failed_packages = []
    for package in packages:
        if not install_package(package):
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("请手动安装: pip install " + " ".join(failed_packages))
    else:
        print("\n✅ 所有Python包安装成功")
    
    # 2. 检查Chrome浏览器
    print("\n🌐 检查Chrome浏览器...")
    chrome_ok = check_chrome_installed()
    
    if not chrome_ok:
        print("请从以下地址下载并安装Chrome浏览器:")
        print("https://www.google.com/chrome/")
    
    # 3. 下载ChromeDriver（可选，webdriver-manager会自动处理）
    print("\n🚗 检查ChromeDriver...")
    if os.path.exists("chromedriver.exe") or os.path.exists("chromedriver"):
        print("✅ ChromeDriver已存在")
    else:
        print("ℹ️  ChromeDriver将由webdriver-manager自动管理")
        # download_chromedriver()  # 可选手动下载
    
    print("\n" + "=" * 60)
    if chrome_ok and not failed_packages:
        print("🎉 所有依赖安装完成！")
        print("现在可以运行: python selenium_login_solution.py")
    else:
        print("⚠️  部分依赖安装失败，请检查上述错误信息")
    print("=" * 60)

if __name__ == "__main__":
    main()
