import urllib.parse
import base64
import json

token = '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'

# URL解码
url_decoded = urllib.parse.unquote(token)
print('URL decoded:', url_decoded)

# Base64解码
base64_decoded = base64.b64decode(url_decoded).decode()
print('Base64 decoded:', base64_decoded)

# 再次URL解码
final_decoded = urllib.parse.unquote(base64_decoded)
print('Final decoded:', final_decoded)

# JSON解析
json_data = json.loads(final_decoded)
print('JSON parsed:')
for key, value in json_data.items():
    print(f'  {key}: {value}')
