import requests
import time
import json
from urllib.parse import urljoin

def deep_api_analysis():
    """深入分析API可用性"""
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    
    print("=== 深入API分析 ===\n")
    
    # 1. 完全模拟浏览器的会话建立过程
    print("1. 完整会话建立过程:")
    
    # 步骤1: 访问登录页面
    login_page_url = f"{base_url}/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3"
    
    page_headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Host': 'login-sso.siyscrm.com',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        page_response = session.get(login_page_url, headers=page_headers)
        print(f"   登录页面: {page_response.status_code}")
        print(f"   Cookies: {len(session.cookies)} 个")
        for cookie in session.cookies:
            print(f"     {cookie.name}: {cookie.value}")
    except Exception as e:
        print(f"   登录页面访问失败: {e}")
        return
    
    # 步骤2: 获取验证码key
    xhr_headers = {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Host': 'login-sso.siyscrm.com',
        'Referer': login_page_url,
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        key_response = session.get(f"{base_url}/Authorize/GetImgVerifyCodeKey", headers=xhr_headers)
        print(f"   验证码key: {key_response.status_code}")
        if key_response.status_code == 200:
            img_code_key = key_response.json()
            print(f"     Key: {img_code_key}")
        else:
            print("     获取验证码key失败")
            return
    except Exception as e:
        print(f"   验证码key获取失败: {e}")
        return
    
    # 步骤3: 在每个步骤后测试API
    print("\n2. 在不同阶段测试API可用性:")
    
    test_url = f"{base_url}/Authorize/postV2"
    
    # 测试1: 仅访问页面后
    try:
        response = session.post(test_url, headers=xhr_headers, timeout=5)
        print(f"   访问页面后: {response.status_code}")
    except Exception as e:
        print(f"   访问页面后: 错误 - {e}")
    
    # 测试2: 获取验证码key后
    try:
        response = session.post(test_url, headers=xhr_headers, timeout=5)
        print(f"   获取验证码key后: {response.status_code}")
    except Exception as e:
        print(f"   获取验证码key后: 错误 - {e}")
    
    # 步骤4: 执行所有预检查请求
    username = "***********"
    client_id = "81c4e66b04066aa8da079f3e5d2bf1f3"
    
    try:
        # 登录验证检查
        verify_response = session.get(f"{base_url}/Authorize/GetLoginVerifCode?userAccount={username}&clientId={client_id}", headers=xhr_headers)
        print(f"   登录验证检查: {verify_response.status_code}")
        
        # 用户信息检查
        mobile_response = session.get(f"{base_url}/Authorize/GetUserMobile?account={username}", headers=xhr_headers)
        print(f"   用户信息检查: {mobile_response.status_code}")
    except Exception as e:
        print(f"   预检查失败: {e}")
    
    # 测试3: 所有预检查后
    try:
        response = session.post(test_url, headers=xhr_headers, timeout=5)
        print(f"   所有预检查后: {response.status_code}")
    except Exception as e:
        print(f"   所有预检查后: 错误 - {e}")
    
    # 3. 测试不同的Content-Type
    print("\n3. 测试不同的Content-Type:")
    
    content_types = [
        'application/json',
        'application/x-www-form-urlencoded',
        'multipart/form-data',
        'text/plain',
        'application/json; charset=utf-8'
    ]
    
    test_payload = {
        'name': '',
        'pwd': '',
        'redirect_uri': '',
        'client_id': '',
        'img_verify_code': 'test',
        'img_verify_code_key': img_code_key,
        'is_mobile_verify': False
    }
    
    for ct in content_types:
        try:
            headers = xhr_headers.copy()
            headers['Content-Type'] = ct
            
            if ct == 'application/json':
                response = session.post(test_url, headers=headers, json=test_payload, timeout=5)
            else:
                response = session.post(test_url, headers=headers, data=test_payload, timeout=5)
            
            print(f"   {ct}: {response.status_code}")
            if response.status_code != 404:
                print(f"     响应: {response.text[:100]}")
        except Exception as e:
            print(f"   {ct}: 错误")
    
    # 4. 检查是否有隐藏的参数要求
    print("\n4. 测试额外的头部参数:")
    
    extra_headers = [
        {'Origin': 'https://login-sso.siyscrm.com'},
        {'X-Forwarded-For': '127.0.0.1'},
        {'X-Real-IP': '127.0.0.1'},
        {'X-CSRF-Token': 'test'},
        {'Authorization': 'Bearer test'},
        {'Cookie': 'test=value'},
        {'Cache-Control': 'no-cache'},
        {'Pragma': 'no-cache'}
    ]
    
    for extra in extra_headers:
        try:
            headers = xhr_headers.copy()
            headers.update(extra)
            
            response = session.post(test_url, headers=headers, timeout=5)
            print(f"   {list(extra.keys())[0]}: {response.status_code}")
        except Exception as e:
            print(f"   {list(extra.keys())[0]}: 错误")
    
    # 5. 检查服务器响应的详细信息
    print("\n5. 详细响应分析:")
    try:
        response = session.post(test_url, headers=xhr_headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   状态文本: {response.reason}")
        print(f"   响应时间: {response.elapsed.total_seconds():.3f}秒")
        print(f"   响应大小: {len(response.content)}字节")
        print(f"   最终URL: {response.url}")
        print(f"   重定向历史: {len(response.history)}次")
        
        print(f"   响应头:")
        for key, value in response.headers.items():
            print(f"     {key}: {value}")
        
        if response.content:
            print(f"   响应内容: {response.content}")
    except Exception as e:
        print(f"   详细分析失败: {e}")

if __name__ == "__main__":
    deep_api_analysis()
