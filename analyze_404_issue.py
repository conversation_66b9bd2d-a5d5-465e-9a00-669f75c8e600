import requests
import time
from urllib.parse import urljoin

def analyze_404_issue():
    """分析404问题的可能原因"""
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    
    print("=== 分析404问题 ===\n")
    
    # 1. 检查不同的HTTP方法
    print("1. 测试不同的HTTP方法:")
    test_url = f"{base_url}/Authorize/postV2"
    
    methods = ['GET', 'POST', 'PUT', 'PATCH']
    for method in methods:
        try:
            response = session.request(method, test_url, timeout=5)
            print(f"   {method}: {response.status_code}")
        except Exception as e:
            print(f"   {method}: 错误 - {e}")
    
    # 2. 检查路径变体
    print("\n2. 测试路径变体:")
    path_variants = [
        "/Authorize/postV2",
        "/authorize/postV2", 
        "/Authorize/PostV2",
        "/authorize/PostV2",
        "/Authorize/postv2",
        "/authorize/postv2",
        "/api/Authorize/postV2",
        "/v2/Authorize/post",
        "/Authorize/post/v2"
    ]
    
    for path in path_variants:
        try:
            response = session.post(f"{base_url}{path}", timeout=5)
            if response.status_code != 404:
                print(f"   ✅ {path}: {response.status_code}")
            else:
                print(f"   ❌ {path}: 404")
        except Exception as e:
            print(f"   ❌ {path}: 错误 - {e}")
    
    # 3. 检查是否需要特定的User-Agent
    print("\n3. 测试不同的User-Agent:")
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
        "PostmanRuntime/7.32.3",
        "python-requests/2.31.0"
    ]
    
    for ua in user_agents:
        try:
            headers = {'User-Agent': ua}
            response = session.post(test_url, headers=headers, timeout=5)
            print(f"   {ua[:30]}...: {response.status_code}")
        except Exception as e:
            print(f"   {ua[:30]}...: 错误")
    
    # 4. 检查是否需要特定的Referer
    print("\n4. 测试Referer依赖:")
    referers = [
        None,
        f"{base_url}/html/apollo_v4/index.html",
        f"{base_url}/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3",
        f"{base_url}/",
        "https://apollo.siyscrm.com/"
    ]
    
    for referer in referers:
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
            }
            if referer:
                headers['Referer'] = referer
            
            response = session.post(test_url, headers=headers, timeout=5)
            referer_display = referer if referer else "无Referer"
            print(f"   {referer_display[:50]}...: {response.status_code}")
        except Exception as e:
            print(f"   {referer_display[:50]}...: 错误")
    
    # 5. 检查时间敏感性
    print("\n5. 测试时间间隔:")
    for i in range(3):
        try:
            response = session.post(test_url, timeout=5)
            print(f"   尝试 {i+1}: {response.status_code}")
            time.sleep(2)
        except Exception as e:
            print(f"   尝试 {i+1}: 错误")
    
    # 6. 检查是否需要先访问其他页面
    print("\n6. 测试页面访问顺序:")
    
    # 先访问主页
    try:
        session.get(f"{base_url}/", timeout=5)
        response = session.post(test_url, timeout=5)
        print(f"   访问主页后: {response.status_code}")
    except Exception as e:
        print(f"   访问主页后: 错误")
    
    # 先访问登录页面
    try:
        login_page = f"{base_url}/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3"
        session.get(login_page, timeout=5)
        response = session.post(test_url, timeout=5)
        print(f"   访问登录页面后: {response.status_code}")
    except Exception as e:
        print(f"   访问登录页面后: 错误")
    
    # 7. 检查响应头信息
    print("\n7. 分析响应头:")
    try:
        response = session.post(test_url, timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   响应头:")
        for key, value in response.headers.items():
            print(f"     {key}: {value}")
        
        if response.text:
            print(f"   响应体: {response.text[:200]}")
    except Exception as e:
        print(f"   错误: {e}")

if __name__ == "__main__":
    analyze_404_issue()
