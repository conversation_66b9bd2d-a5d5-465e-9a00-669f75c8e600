import requests
import hashlib
import time
import base64
import json
from urllib.parse import quote, urljoin
import os

def build_token(username, password, client_id, redirect_uri, access_code, timestamp):
    """构造token - 完全按照浏览器的方式"""
    token_data = {
        "name": username,
        "pwd": password,
        "redirect_uri": redirect_uri,
        "client_id": client_id,
        "access_code": access_code,
        "timestamp": timestamp
    }
    json_str = json.dumps(token_data, separators=(',', ':'))
    url_encoded = quote(json_str)
    base64_encoded = base64.b64encode(url_encoded.encode('utf-8')).decode('utf-8')
    return quote(base64_encoded)

def get_captcha_key_and_image(session, base_url):
    """获取验证码key和图片"""
    # 获取验证码key
    key_url = f"{base_url}/Authorize/GetImgVerifyCodeKey"
    headers = {
        'Accept': '*/*',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Referer': f'{base_url}/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3'
    }
    
    try:
        response = session.get(key_url, headers=headers)
        response.raise_for_status()
        img_code_key = response.json()
        print(f"[+] 成功获取验证码Key: {img_code_key}")
    except Exception as e:
        print(f"[-] 获取验证码Key失败: {e}")
        return None, None
    
    # 下载验证码图片
    img_url = f"{base_url}/Authorize/GetImgVerifyCode?key={img_code_key}"
    try:
        img_response = session.get(img_url, headers=headers)
        img_response.raise_for_status()
        
        captcha_filename = f'captcha_{int(time.time())}.jpg'
        with open(captcha_filename, 'wb') as f:
            f.write(img_response.content)
        print(f"[+] 验证码图片已保存到: {captcha_filename}")
        
        return img_code_key, captcha_filename
    except Exception as e:
        print(f"[-] 下载验证码图片失败: {e}")
        return img_code_key, None

def fixed_login_solution():
    """基于分析结果的修复版本"""
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    
    # 用户凭据
    username = "***********"
    password = "aabb6688"
    client_id = "81c4e66b04066aa8da079f3e5d2bf1f3"
    redirect_uri = "https://apollo.siyscrm.com/account/login/loginsuccess"
    
    print("[+] 开始修复版登录流程...")
    
    # 1. 访问登录页面建立会话
    login_page_url = f"{base_url}/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3"
    
    page_headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Upgrade-Insecure-Requests': '1'
    }
    
    try:
        page_response = session.get(login_page_url, headers=page_headers)
        page_response.raise_for_status()
        print(f"[+] 登录页面访问成功")
    except Exception as e:
        print(f"[-] 访问登录页面失败: {e}")
        return False
    
    # 2. 获取验证码
    img_code_key, captcha_filename = get_captcha_key_and_image(session, base_url)
    if not img_code_key or not captcha_filename:
        print("[-] 无法获取验证码，退出")
        return False
    
    # 3. 用户输入验证码
    print(f"[+] 请查看验证码图片: {captcha_filename}")
    img_code = input("请输入验证码: ").strip()
    if not img_code:
        print("[-] 验证码不能为空")
        return False
    
    # 4. 执行预检查
    xhr_headers = {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Host': 'login-sso.siyscrm.com',
        'Referer': login_page_url,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        # 登录验证检查
        verify_url = f"{base_url}/Authorize/GetLoginVerifCode?userAccount={username}&clientId={client_id}"
        verify_response = session.get(verify_url, headers=xhr_headers)
        print(f"[+] 预检查完成: {verify_response.status_code}")
        
        # 用户信息检查
        mobile_url = f"{base_url}/Authorize/GetUserMobile?account={username}"
        mobile_response = session.get(mobile_url, headers=xhr_headers)
        print(f"[+] 用户信息检查完成: {mobile_response.status_code}")
    except Exception as e:
        print(f"[-] 预检查失败: {e}")
    
    # 5. 构造登录参数
    access_code = hashlib.md5(password.encode('utf-8')).hexdigest().upper()
    timestamp = int(time.time() * 1000)
    token = build_token(username, password, client_id, redirect_uri, access_code, timestamp)
    
    print(f"[+] 构造登录参数完成")
    print(f"[+] Token: {token[:50]}...")
    
    # 6. 尝试多种登录方式
    login_attempts = [
        # 尝试1: 使用发现的有效端点 /Authorize/post/v2
        {
            'url': f"{base_url}/Authorize/post/v2",
            'method': 'form',
            'description': '有效端点 + Form数据'
        },
        # 尝试2: 原端点 + Form数据
        {
            'url': f"{base_url}/Authorize/postV2",
            'method': 'form',
            'description': '原端点 + Form数据'
        },
        # 尝试3: 有效端点 + JSON数据
        {
            'url': f"{base_url}/Authorize/post/v2",
            'method': 'json',
            'description': '有效端点 + JSON数据'
        },
        # 尝试4: 原端点 + 无Content-Type
        {
            'url': f"{base_url}/Authorize/postV2",
            'method': 'raw',
            'description': '原端点 + 无Content-Type'
        }
    ]
    
    login_payload = {
        'name': '',
        'pwd': '',
        'redirect_uri': '',
        'client_id': '',
        'img_verify_code': img_code,
        'img_verify_code_key': img_code_key,
        'is_mobile_verify': False
    }
    
    for attempt in login_attempts:
        print(f"\n[+] 尝试: {attempt['description']}")
        
        try:
            headers = xhr_headers.copy()
            headers['token'] = token
            headers['Origin'] = 'https://login-sso.siyscrm.com'
            
            if attempt['method'] == 'json':
                headers['Content-Type'] = 'application/json'
                response = session.post(attempt['url'], headers=headers, json=login_payload)
            elif attempt['method'] == 'form':
                headers['Content-Type'] = 'application/x-www-form-urlencoded'
                response = session.post(attempt['url'], headers=headers, data=login_payload)
            else:  # raw
                # 不设置Content-Type，让requests自动处理
                response = session.post(attempt['url'], headers=headers, data=login_payload)
            
            print(f"    状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"    响应: {result}")
                    
                    if result.get("Status") == 0:
                        print("[+] 登录成功!")
                        return True
                    else:
                        print(f"    登录失败: {result.get('Message', '未知错误')}")
                except json.JSONDecodeError:
                    print(f"    非JSON响应: {response.text[:200]}")
                    if "apollo.siyscrm.com" in response.text:
                        print("[+] 登录可能成功 (重定向)")
                        return True
            elif response.status_code in [404, 415]:
                print(f"    {response.status_code} - 继续尝试下一种方法")
            else:
                print(f"    意外状态码: {response.status_code}")
                print(f"    响应: {response.text[:200]}")
                
        except Exception as e:
            print(f"    请求异常: {e}")
    
    print("[-] 所有登录尝试都失败了")
    return False

if __name__ == "__main__":
    try:
        success = fixed_login_solution()
        if success:
            print("\n[+] 登录流程成功完成!")
        else:
            print("\n[-] 登录流程失败")
    finally:
        # 清理验证码文件
        import glob
        for f in glob.glob('captcha_*.jpg'):
            try:
                os.remove(f)
                print(f"[+] 已清理: {f}")
            except:
                pass
