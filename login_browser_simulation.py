import requests
import hashlib
import time
import base64
import json
from urllib.parse import quote, urljoin
import os

def build_token(username, password, client_id, redirect_uri, access_code, timestamp):
    """构造token - 完全按照浏览器的方式"""
    token_data = {
        "name": username,
        "pwd": password,
        "redirect_uri": redirect_uri,
        "client_id": client_id,
        "access_code": access_code,
        "timestamp": timestamp
    }
    json_str = json.dumps(token_data, separators=(',', ':'))
    url_encoded = quote(json_str)
    base64_encoded = base64.b64encode(url_encoded.encode('utf-8')).decode('utf-8')
    return quote(base64_encoded)

def simulate_browser_login():
    """完全模拟浏览器的登录行为"""
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    
    # 用户凭据
    username = "***********"
    password = "aabb6688"
    client_id = "81c4e66b04066aa8da079f3e5d2bf1f3"
    redirect_uri = "https://apollo.siyscrm.com/account/login/loginsuccess"
    
    # 1. 首先访问登录页面 - 完全模拟浏览器
    login_page_url = f"{base_url}/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3"
    
    page_headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Host': 'login-sso.siyscrm.com',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        print("[+] 访问登录页面...")
        page_response = session.get(login_page_url, headers=page_headers)
        page_response.raise_for_status()
        print(f"[+] 登录页面访问成功: {page_response.status_code}")
        print(f"[+] Cookies: {session.cookies}")
    except Exception as e:
        print(f"[-] 访问登录页面失败: {e}")
        return False
    
    # 2. 获取验证码key
    print("[+] 获取验证码key...")
    key_url = f"{base_url}/Authorize/GetImgVerifyCodeKey"
    xhr_headers = {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Host': 'login-sso.siyscrm.com',
        'Referer': login_page_url,
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    try:
        key_response = session.get(key_url, headers=xhr_headers)
        key_response.raise_for_status()
        img_code_key = key_response.json()
        print(f"[+] 验证码key: {img_code_key}")
    except Exception as e:
        print(f"[-] 获取验证码key失败: {e}")
        return False
    
    # 3. 下载验证码图片
    print("[+] 下载验证码图片...")
    img_url = f"{base_url}/Authorize/GetImgVerifyCode?key={img_code_key}"
    try:
        img_response = session.get(img_url, headers=xhr_headers)
        img_response.raise_for_status()
        
        captcha_filename = f'captcha_{int(time.time())}.jpg'
        with open(captcha_filename, 'wb') as f:
            f.write(img_response.content)
        print(f"[+] 验证码已保存: {captcha_filename}")
    except Exception as e:
        print(f"[-] 下载验证码失败: {e}")
        return False
    
    # 4. 用户输入验证码
    img_code = input("请输入验证码: ").strip()
    if not img_code:
        print("[-] 验证码不能为空")
        return False
    
    # 5. 模拟浏览器的预检查请求
    print("[+] 执行登录预检查...")
    verify_url = f"{base_url}/Authorize/GetLoginVerifCode?userAccount={username}&clientId={client_id}"
    try:
        verify_response = session.get(verify_url, headers=xhr_headers)
        verify_response.raise_for_status()
        verify_result = verify_response.json()
        print(f"[+] 预检查结果: {verify_result}")
    except Exception as e:
        print(f"[-] 预检查失败: {e}")
    
    # 6. 获取用户信息
    mobile_url = f"{base_url}/Authorize/GetUserMobile?account={username}"
    try:
        mobile_response = session.get(mobile_url, headers=xhr_headers)
        mobile_response.raise_for_status()
        mobile_result = mobile_response.json()
        print(f"[+] 用户信息: {mobile_result}")
    except Exception as e:
        print(f"[-] 获取用户信息失败: {e}")
    
    # 7. 构造登录请求 - 完全按照浏览器的方式
    access_code = hashlib.md5(password.encode('utf-8')).hexdigest().upper()
    timestamp = int(time.time() * 1000)
    token = build_token(username, password, client_id, redirect_uri, access_code, timestamp)
    
    print(f"[+] 构造登录参数...")
    print(f"[+] Access Code: {access_code}")
    print(f"[+] Timestamp: {timestamp}")
    print(f"[+] Token: {token[:50]}...")
    
    # 8. 发送登录请求 - 使用完全相同的头部
    login_url = f"{base_url}/Authorize/postV2"
    login_headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Host': 'login-sso.siyscrm.com',
        'Origin': 'https://login-sso.siyscrm.com',
        'Referer': login_page_url,
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'token': token
    }
    
    login_payload = {
        'name': '',
        'pwd': '',
        'redirect_uri': '',
        'client_id': '',
        'img_verify_code': img_code,
        'img_verify_code_key': img_code_key,
        'is_mobile_verify': False
    }
    
    try:
        print("[+] 发送登录请求...")
        login_response = session.post(login_url, headers=login_headers, json=login_payload)
        
        print(f"[+] 响应状态码: {login_response.status_code}")
        print(f"[+] 响应头: {dict(login_response.headers)}")
        
        if login_response.status_code == 200:
            try:
                result = login_response.json()
                print(f"[+] 登录响应: {result}")
                
                if result.get("Status") == 0:
                    print("[+] 登录成功!")
                    return True
                else:
                    print(f"[-] 登录失败: {result.get('Message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                print(f"[+] 非JSON响应: {login_response.text[:500]}")
                # 可能是重定向成功
                if "apollo.siyscrm.com" in login_response.text:
                    print("[+] 登录可能成功 (重定向)")
                    return True
                return False
        else:
            print(f"[-] 登录请求失败: {login_response.status_code}")
            print(f"[-] 响应内容: {login_response.text}")
            return False
            
    except Exception as e:
        print(f"[-] 登录请求异常: {e}")
        return False
    
    finally:
        # 清理验证码文件
        try:
            if os.path.exists(captcha_filename):
                os.remove(captcha_filename)
                print(f"[+] 已清理验证码文件")
        except:
            pass

if __name__ == "__main__":
    simulate_browser_login()
