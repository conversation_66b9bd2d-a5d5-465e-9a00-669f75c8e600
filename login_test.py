import requests
import hashlib
import time
import base64
import json
from urllib.parse import quote, urljoin
import ddddocr

def build_token(username, password, client_id, redirect_uri, access_code, timestamp):
    """根据参数构造加密的token"""
    token_data = {
        "name": username,
        "pwd": password,
        "redirect_uri": redirect_uri,
        "client_id": client_id,
        "access_code": access_code,
        "timestamp": timestamp
    }
    json_str = json.dumps(token_data, separators=(',', ':'))
    return quote(base64.b64encode(json_str.encode('utf-8')).decode('utf-8'))

def perform_login():
    """
    执行登录流程
    """
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    ocr = ddddocr.DdddOcr()

    # 1. 直接调用API获取验证码的key
    key_url = urljoin(base_url, "/Authorize/GetImgVerifyCodeKey")
    try:
        # 添加必要的请求头，模拟浏览器XHR请求
        headers = {
            'Accept': '*/*',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = session.get(key_url, headers=headers)
        response.raise_for_status()
        # API直接返回key的字符串，包含双引号，需要去除
        img_code_key = response.json()
        print(f"[+] 成功获取验证码Key: {img_code_key}")
    except requests.exceptions.RequestException as e:
        print(f"[-] 获取验证码Key失败: {e}")
        return
    except json.JSONDecodeError:
        print(f"[-] 解析验证码Key响应失败: {response.text}")
        return

    # 2. 下载验证码图片并使用OCR识别
    img_url = urljoin(base_url, f"/Authorize/GetImgVerifyCode?key={img_code_key}")
    try:
        img_response = session.get(img_url)
        img_response.raise_for_status()
        img_code = ocr.classification(img_response.content)
        print(f"[+] 验证码识别成功: {img_code}")
    except requests.exceptions.RequestException as e:
        print(f"[-] 下载验证码图片失败: {e}")
        return
    except Exception as e:
        print(f"[-] OCR识别失败: {e}")
        return

    # 3. 准备登录参数
    username = "***********"
    password = "aabb6688"
    client_id = "81c4e66b04066aa8da079f3e5d2bf1f3"
    redirect_uri = "https://apollo.siyscrm.com/account/login/loginsuccess"
    
    # 假设 access_code 的生成逻辑，这仍然是目前最不确定的部分
    access_code = hashlib.md5(password.encode('utf-8')).hexdigest().upper()
    timestamp = int(time.time() * 1000)
    
    # 4. 构建token
    token = build_token(username, password, client_id, redirect_uri, access_code, timestamp)

    # 5. 发送登录请求
    login_api_url = urljoin(base_url, "/Authorize/postV2")
    login_headers = {
        'token': token,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    login_payload = {
        'img_verify_code': img_code,
        'img_verify_code_key': img_code_key
    }

    try:
        response = session.post(login_api_url, headers=login_headers, data=login_payload)
        response.raise_for_status()
        result = response.json()
        
        if result.get("Status") == 0:
            print("[+] 登录成功!")
            print(f"响应内容: {result}")
        else:
            print(f"[-] 登录失败: {result.get('Message', '未知错误')}")
            print(f"详细响应: {result}")

    except requests.exceptions.RequestException as e:
        print(f"[-] 登录请求异常: {e}")
    except json.JSONDecodeError:
        print(f"[-] 解析登录响应失败: {response.text}")

if __name__ == "__main__":
    perform_login()