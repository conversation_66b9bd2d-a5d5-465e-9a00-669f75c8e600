import requests
import hashlib
import time
import base64
import json
from urllib.parse import quote, urljoin

def build_token(username, password, client_id, redirect_uri, access_code, timestamp):
    """根据真实浏览器请求构造token"""
    token_data = {
        "name": username,
        "pwd": password,
        "redirect_uri": redirect_uri,
        "client_id": client_id,
        "access_code": access_code,
        "timestamp": timestamp
    }
    json_str = json.dumps(token_data, separators=(',', ':'))
    # 先URL编码JSON字符串
    url_encoded = quote(json_str)
    # 再Base64编码
    base64_encoded = base64.b64encode(url_encoded.encode('utf-8')).decode('utf-8')
    # 最后再次URL编码
    return quote(base64_encoded)

def debug_login():
    """调试登录请求"""
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    
    # 用户凭据
    username = "***********"
    password = "aabb6688"
    client_id = "81c4e66b04066aa8da079f3e5d2bf1f3"
    redirect_uri = "https://apollo.siyscrm.com/account/login/loginsuccess"
    
    # 生成access_code
    access_code = hashlib.md5(password.encode('utf-8')).hexdigest().upper()
    timestamp = int(time.time() * 1000)
    
    # 构建token
    token = build_token(username, password, client_id, redirect_uri, access_code, timestamp)
    
    print(f"Token: {token}")
    print(f"Access Code: {access_code}")
    print(f"Timestamp: {timestamp}")
    
    # 测试不同的URL路径
    test_urls = [
        f"{base_url}/Authorize/postV2",
        f"{base_url}/authorize/postV2",
        f"{base_url}/Authorize/PostV2",
        f"{base_url}/authorize/PostV2"
    ]
    
    for test_url in test_urls:
        print(f"\n测试URL: {test_url}")
        
        headers = {
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'token': token
        }
        
        payload = {
            'name': '',
            'pwd': '',
            'redirect_uri': '',
            'client_id': '',
            'img_verify_code': 'test',
            'img_verify_code_key': 'test-key',
            'is_mobile_verify': False
        }
        
        try:
            response = session.post(test_url, headers=headers, json=payload, timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text[:200]}")
            
            if response.status_code != 404:
                print("✅ 找到有效的URL!")
                break
                
        except Exception as e:
            print(f"错误: {e}")
    
    # 测试简单的GET请求到基础路径
    print(f"\n测试基础路径访问:")
    try:
        base_response = session.get(f"{base_url}/Authorize/", timeout=10)
        print(f"基础路径状态码: {base_response.status_code}")
    except Exception as e:
        print(f"基础路径错误: {e}")

if __name__ == "__main__":
    debug_login()
