import requests
import hashlib
import time
import base64
import json
from urllib.parse import quote, urljoin
import os

def build_token(username, password, client_id, redirect_uri, access_code, timestamp):
    """根据参数构造加密的token - 双重URL编码 + Base64编码"""
    token_data = {
        "name": username,
        "pwd": password,
        "redirect_uri": redirect_uri,
        "client_id": client_id,
        "access_code": access_code,
        "timestamp": timestamp
    }
    json_str = json.dumps(token_data, separators=(',', ':'))
    # 先URL编码JSON字符串
    url_encoded = quote(json_str)
    # 再Base64编码
    base64_encoded = base64.b64encode(url_encoded.encode('utf-8')).decode('utf-8')
    # 最后再次URL编码
    return quote(base64_encoded)

def get_captcha_key_and_image(session, base_url):
    """获取验证码key和图片"""
    # 1. 获取验证码key
    key_url = urljoin(base_url, "/Authorize/GetImgVerifyCodeKey")
    headers = {
        'Accept': '*/*',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    try:
        response = session.get(key_url, headers=headers)
        response.raise_for_status()
        img_code_key = response.json()
        print(f"[+] 成功获取验证码Key: {img_code_key}")
    except Exception as e:
        print(f"[-] 获取验证码Key失败: {e}")
        return None, None
    
    # 2. 下载验证码图片
    img_url = urljoin(base_url, f"/Authorize/GetImgVerifyCode?key={img_code_key}")
    try:
        img_response = session.get(img_url, headers=headers)
        img_response.raise_for_status()
        
        # 保存验证码图片
        captcha_filename = f'captcha_{int(time.time())}.jpg'
        with open(captcha_filename, 'wb') as f:
            f.write(img_response.content)
        print(f"[+] 验证码图片已保存到: {captcha_filename}")
        
        return img_code_key, captcha_filename
    except Exception as e:
        print(f"[-] 下载验证码图片失败: {e}")
        return img_code_key, None

def perform_login_with_captcha(username, password, img_code, img_code_key):
    """执行登录流程"""
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    
    # 登录参数
    client_id = "81c4e66b04066aa8da079f3e5d2bf1f3"
    redirect_uri = "https://apollo.siyscrm.com/account/login/loginsuccess"
    
    # 生成access_code (根据观察到的请求，这似乎是密码的MD5哈希的大写形式)
    access_code = hashlib.md5(password.encode('utf-8')).hexdigest().upper()
    timestamp = int(time.time() * 1000)
    
    # 构建token
    token = build_token(username, password, client_id, redirect_uri, access_code, timestamp)
    
    # 发送登录请求
    login_api_url = urljoin(base_url, "/Authorize/postV2")
    login_headers = {
        'token': token,
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'X-Requested-With': 'XMLHttpRequest',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Referer': 'https://login-sso.siyscrm.com/html/apollo_v4/index.html'
    }
    
    # 根据真实请求的格式构造payload
    login_payload = {
        'name': '',  # 在真实请求中这些字段是空的，因为信息在token中
        'pwd': '',
        'redirect_uri': '',
        'client_id': '',
        'img_verify_code': img_code,
        'img_verify_code_key': img_code_key,
        'is_mobile_verify': False
    }
    
    try:
        print(f"[+] 发送登录请求...")
        print(f"[+] Token: {token[:50]}...")
        print(f"[+] 验证码: {img_code}")
        
        response = session.post(login_api_url, headers=login_headers, json=login_payload)
        response.raise_for_status()
        result = response.json()
        
        print(f"[+] 登录响应: {result}")
        
        if result.get("Status") == 0:
            print("[+] 登录成功!")
            return True, result
        else:
            print(f"[-] 登录失败: {result.get('Message', '未知错误')}")
            return False, result
            
    except requests.exceptions.RequestException as e:
        print(f"[-] 登录请求异常: {e}")
        return False, None
    except json.JSONDecodeError:
        print(f"[-] 解析登录响应失败: {response.text}")
        return False, None

def main():
    """主函数"""
    session = requests.Session()
    base_url = "https://login-sso.siyscrm.com"
    
    # 用户凭据
    username = "13995933053"
    password = "aabb6688"
    
    print("[+] 开始登录流程...")
    
    # 获取验证码
    img_code_key, captcha_filename = get_captcha_key_and_image(session, base_url)
    
    if not img_code_key or not captcha_filename:
        print("[-] 无法获取验证码，退出")
        return
    
    # 手动输入验证码（在实际应用中可以集成OCR）
    print(f"[+] 请查看验证码图片: {captcha_filename}")
    img_code = input("请输入验证码: ").strip()
    
    if not img_code:
        print("[-] 验证码不能为空")
        return
    
    # 执行登录
    success, result = perform_login_with_captcha(username, password, img_code, img_code_key)
    
    if success:
        print("[+] 登录流程完成!")
    else:
        print("[-] 登录失败")
    
    # 清理验证码文件
    try:
        if os.path.exists(captcha_filename):
            os.remove(captcha_filename)
            print(f"[+] 已清理验证码文件: {captcha_filename}")
    except:
        pass

if __name__ == "__main__":
    main()
