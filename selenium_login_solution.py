"""
私域网站爬虫 - Selenium自动化浏览器解决方案
基于真实浏览器环境，完全绕过反爬虫检测
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import time
import requests
import os
import base64
from PIL import Image
import io

class SeleniumLoginSolution:
    def __init__(self, headless=False):
        """
        初始化Selenium浏览器
        Args:
            headless: 是否使用无头模式（True=后台运行，False=显示浏览器窗口）
        """
        self.driver = None
        self.wait = None
        self.headless = headless
        self.setup_driver()
    
    def setup_driver(self):
        """配置Chrome浏览器"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
        
        # 反检测配置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
        
        # 窗口大小
        chrome_options.add_argument('--window-size=1920,1080')
        
        try:
            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            print("[+] Chrome浏览器启动成功")
        except Exception as e:
            print(f"[-] 浏览器启动失败: {e}")
            print("请确保已安装Chrome浏览器")
            print("如果问题持续，请运行: python install_dependencies.py")
            raise
    
    def login(self, username="***********", password="aabb6688"):
        """
        执行自动登录
        Args:
            username: 用户名
            password: 密码
        Returns:
            bool: 登录是否成功
        """
        try:
            print("🚀 开始Selenium自动登录...")
            
            # 1. 访问登录页面
            login_url = "https://login-sso.siyscrm.com/html/apollo_v4/index.html?redirect_uri=https%3a%2f%2fapollo.siyscrm.com%2faccount%2flogin%2floginsuccess&client_id=81c4e66b04066aa8da079f3e5d2bf1f3"
            print(f"[+] 访问登录页面: {login_url}")
            self.driver.get(login_url)
            
            # 等待页面加载
            time.sleep(3)

            # 调试：打印页面标题确认页面加载
            print(f"[DEBUG] 页面标题: {self.driver.title}")
            print(f"[DEBUG] 当前URL: {self.driver.current_url}")
            
            # 2. 填写用户名
            print("[+] 填写用户名...")
            username_input = self.wait.until(EC.presence_of_element_located((By.ID, "userAccount")))
            username_input.clear()
            username_input.send_keys(username)
            
            # 3. 填写密码
            print("[+] 填写密码...")
            password_input = self.driver.find_element(By.ID, "pwd")
            password_input.clear()
            password_input.send_keys(password)
            
            # 4. 处理验证码
            print("[+] 处理验证码...")
            captcha_success = self.handle_captcha()
            if not captcha_success:
                print("[-] 验证码处理失败")
                return False
            
            # 5. 点击登录按钮
            print("[+] 点击登录按钮...")
            login_button = None
            button_selectors = [
                (By.CSS_SELECTOR, "button.login-form__btn-submit"),
                (By.CSS_SELECTOR, "button[type='submit']"),
                (By.CSS_SELECTOR, "input[type='submit']"),
                (By.XPATH, "//button[contains(text(), '登录') or contains(text(), '登 录')]"),
                (By.CSS_SELECTOR, ".btn-submit"),
                (By.CSS_SELECTOR, ".login-btn")
            ]

            for by, selector in button_selectors:
                try:
                    login_button = self.driver.find_element(by, selector)
                    print(f"[+] 找到登录按钮: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if not login_button:
                print("[-] 未找到登录按钮")
                return False

            login_button.click()
            
            # 6. 等待登录结果
            print("[+] 等待登录结果...")
            return self.wait_for_login_result()
            
        except TimeoutException:
            print("[-] 页面加载超时")
            return False
        except NoSuchElementException as e:
            print(f"[-] 页面元素未找到: {e}")
            return False
        except Exception as e:
            print(f"[-] 登录过程出错: {e}")
            return False
    
    def handle_captcha(self):
        """处理验证码"""
        try:
            # 等待页面完全加载
            time.sleep(2)

            # 多种方式查找验证码图片（基于真实页面结构）
            captcha_img = None
            selectors = [
                (By.ID, "mixImg"),  # 真实的验证码图片ID
                (By.CSS_SELECTOR, "img[src*='GetImgVerifyCode']"),
                (By.CSS_SELECTOR, "img[onclick*='refreshCode']"),
                (By.XPATH, "//img[contains(@src, 'GetImgVerifyCode')]"),
                (By.CSS_SELECTOR, ".verify-code img"),
                (By.CSS_SELECTOR, ".captcha img")
            ]

            for by, selector in selectors:
                try:
                    captcha_img = self.driver.find_element(by, selector)
                    print(f"[+] 找到验证码图片: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if not captcha_img:
                print("[-] 未找到验证码图片元素")
                # 打印页面源码用于调试
                print("页面中的img元素:")
                img_elements = self.driver.find_elements(By.TAG_NAME, "img")
                for i, img in enumerate(img_elements[:5]):  # 只显示前5个
                    try:
                        print(f"  img[{i}]: id='{img.get_attribute('id')}', src='{img.get_attribute('src')[:100]}'")
                    except:
                        pass
                return False

            # 截取验证码图片
            captcha_screenshot = captcha_img.screenshot_as_png

            # 保存验证码图片
            captcha_filename = f"captcha_selenium_{int(time.time())}.png"
            with open(captcha_filename, 'wb') as f:
                f.write(captcha_screenshot)

            print(f"[+] 验证码已保存: {captcha_filename}")

            # 用户输入验证码
            captcha_code = input("请输入验证码: ").strip()

            if not captcha_code:
                print("[-] 验证码不能为空")
                return False

            # 多种方式查找验证码输入框
            captcha_input = None
            input_selectors = [
                (By.ID, "pictureVerifVode"),
                (By.ID, "pictureVerifyCode"),
                (By.ID, "verifyCode"),
                (By.CSS_SELECTOR, "input[placeholder*='验证码']"),
                (By.CSS_SELECTOR, "input[name*='verify']"),
                (By.XPATH, "//input[contains(@placeholder, '验证码')]")
            ]

            for by, selector in input_selectors:
                try:
                    captcha_input = self.driver.find_element(by, selector)
                    print(f"[+] 找到验证码输入框: {selector}")
                    break
                except NoSuchElementException:
                    continue

            if not captcha_input:
                print("[-] 未找到验证码输入框")
                return False

            # 填写验证码
            captcha_input.clear()
            captcha_input.send_keys(captcha_code)

            # 清理验证码文件
            try:
                os.remove(captcha_filename)
                print("[+] 已清理验证码文件")
            except:
                pass

            return True

        except Exception as e:
            print(f"[-] 验证码处理失败: {e}")
            return False
    
    def wait_for_login_result(self, timeout=15):
        """等待登录结果"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            current_url = self.driver.current_url
            
            # 检查是否跳转到目标页面
            if "apollo.siyscrm.com" in current_url and "login" not in current_url:
                print("🎉 登录成功!")
                print(f"[+] 当前URL: {current_url}")
                
                # 等待页面完全加载
                time.sleep(3)
                
                # 获取页面标题和内容
                page_title = self.driver.title
                print(f"[+] 页面标题: {page_title}")
                
                # 打印页面内容
                self.print_page_content()
                
                return True
            
            # 检查是否有错误信息
            try:
                error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".error, .alert, .message")
                for element in error_elements:
                    if element.is_displayed() and element.text.strip():
                        print(f"[-] 登录错误: {element.text}")
                        return False
            except:
                pass
            
            time.sleep(1)
        
        print("[-] 登录超时")
        return False
    
    def print_page_content(self):
        """打印登录成功后的页面内容"""
        try:
            print("📄 登录成功后的页面内容:")
            print("=" * 80)
            
            # 获取页面文本内容
            body_text = self.driver.find_element(By.TAG_NAME, "body").text
            print(body_text[:1000])  # 打印前1000个字符
            
            print("=" * 80)
            
            # 获取主要导航菜单
            try:
                nav_elements = self.driver.find_elements(By.CSS_SELECTOR, "nav a, .nav a, .menu a, .sidebar a")
                if nav_elements:
                    print("🔗 主要功能菜单:")
                    for nav in nav_elements[:10]:  # 只显示前10个
                        if nav.text.strip():
                            print(f"  - {nav.text.strip()}")
            except:
                pass
            
        except Exception as e:
            print(f"[-] 获取页面内容失败: {e}")
    
    def get_cookies(self):
        """获取登录后的Cookies"""
        try:
            cookies = self.driver.get_cookies()
            print("🍪 登录Cookies:")
            for cookie in cookies:
                print(f"  {cookie['name']}: {cookie['value']}")
            return cookies
        except Exception as e:
            print(f"[-] 获取Cookies失败: {e}")
            return []
    
    def extract_session_for_requests(self):
        """提取会话信息用于requests库"""
        try:
            cookies = self.driver.get_cookies()
            session = requests.Session()
            
            # 添加cookies到requests session
            for cookie in cookies:
                session.cookies.set(cookie['name'], cookie['value'])
            
            # 添加常用请求头
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })
            
            print("✅ 会话信息已提取到requests.Session对象")
            return session
            
        except Exception as e:
            print(f"[-] 提取会话信息失败: {e}")
            return None
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("[+] 浏览器已关闭")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 私域网站爬虫 - Selenium自动化解决方案")
    print("=" * 60)
    
    # 创建登录实例
    login_bot = SeleniumLoginSolution(headless=False)  # 设置为True可后台运行
    
    try:
        # 执行登录
        success = login_bot.login()
        
        if success:
            print("✅ 登录流程成功完成!")
            
            # 获取Cookies
            cookies = login_bot.get_cookies()
            
            # 提取会话信息
            session = login_bot.extract_session_for_requests()
            
            if session:
                print("🔧 现在可以使用session对象进行后续的数据抓取")
                print("示例: response = session.get('https://apollo.siyscrm.com/api/data')")
            
            # 保持浏览器打开一段时间供用户查看
            input("按Enter键关闭浏览器...")
            
        else:
            print("❌ 登录失败")
            
    except KeyboardInterrupt:
        print("\n[!] 用户中断操作")
    except Exception as e:
        print(f"[-] 程序执行出错: {e}")
    finally:
        login_bot.close()

if __name__ == "__main__":
    main()
